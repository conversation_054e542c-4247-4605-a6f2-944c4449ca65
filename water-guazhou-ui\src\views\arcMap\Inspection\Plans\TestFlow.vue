<template>
  <div class="fullscreen-iframe-container">
    <!-- 主内容区 -->
    <div class="iframe-wrapper">
      <!-- 加载动画 -->
      <div v-if="loading" class="loading-overlay">
        <div class="spinner-container">
          <div class="spinner"></div>
          <div class="loading-text">正在连接工作流系统...</div>
          <div class="loading-subtext">请稍候，系统正在初始化</div>
        </div>
      </div>
      
      <!-- 状态指示器 -->
      <div class="connection-status" :class="statusClass">
        <span class="status-indicator"></span>
        {{ statusText }}
      </div>

      <!-- 代理切换按钮 -->
      <div class="proxy-toggle">
        <button @click="toggleProxy" class="toggle-btn">
          {{ useProxy ? '使用代理' : '直接访问' }}
        </button>
      </div>
      
      <!-- 系统水印 -->
      <div class="system-watermark">嵌入式工作流系统 v1.0</div>
      
      <!-- 嵌入的iframe -->
      <iframe
        ref="workflowFrame"
        :src="iframeSrc"
        @load="onIframeLoad"
        class="embedded-frame"
        sandbox="allow-same-origin allow-scripts allow-forms allow-popups allow-top-navigation"
        allow="fullscreen"
      ></iframe>
    </div>
    
    <!-- 底部状态栏 -->
    <div class="system-footer">
      <div class="status-item">
        <span class="label">连接状态：</span>
        <span class="value">{{ statusText }}</span>
      </div>
      <div class="status-item">
        <span class="label">加载时间：</span>
        <span class="value">{{ loadTime }} 秒</span>
      </div>
      <div class="status-item">
        <span class="label">当前时间：</span>
        <span class="value">{{ currentTime }}</span>
      </div>
    </div>
  </div>
</template>

<script>
export default {
  name: 'FullscreenIframeEmbed',
  data() {
    return {
      loading: true,
      connectionStatus: 'connecting', // connecting, connected, error
      loadStartTime: null,
      loadTime: 0,
      currentTime: this.getFormattedTime(),
      timer: null,
      useProxy: true // 是否使用代理，可以根据需要切换
    }
  },
  computed: {
    iframeSrc() {
      // 方案1：使用后端代理（推荐）
      if (this.useProxy) {
        return '/api/workflow/proxy/workflow/processDefinition';
      }

      // 方案2：直接访问原始URL（可能需要目标系统支持CORS）
      return 'http://**********:5666/workflow/processDefinition';
    },
    statusText() {
      return {
        'connecting': '连接中...',
        'connected': '已连接',
        'error': '连接失败'
      }[this.connectionStatus]
    },
    statusClass() {
      return {
        'connecting': 'status-connecting',
        'connected': 'status-connected',
        'error': 'status-error'
      }[this.connectionStatus]
    }
  },
  mounted() {
    // 记录加载开始时间
    this.loadStartTime = new Date();

    // 启动定时器更新时间和加载时间
    this.timer = setInterval(() => {
      this.currentTime = this.getFormattedTime();

      // 计算加载时间（如果仍在加载中）
      if (this.loading) {
        this.loadTime = ((new Date() - this.loadStartTime) / 1000).toFixed(1);
      }
    }, 100);


  },
  beforeDestroy() {
    // 清除定时器
    if (this.timer) {
      clearInterval(this.timer);
    }


  },
  methods: {
    onIframeLoad() {
      console.log('iframe 加载完成');

      // 模拟加载延迟
      setTimeout(() => {
        this.loading = false;
        this.connectionStatus = 'connected';
        console.log('iframe 连接成功，认证信息已通过后端代理注入');
      }, 1500);
    },

    toggleProxy() {
      this.useProxy = !this.useProxy;
      this.loading = true;
      this.connectionStatus = 'connecting';
      console.log(`切换到${this.useProxy ? '代理' : '直接访问'}模式`);

      // 重新加载iframe
      this.$nextTick(() => {
        const iframe = this.$refs.workflowFrame;
        if (iframe) {
          iframe.src = this.iframeSrc;
        }
      });
    },

    getFormattedTime() {
      const now = new Date();
      return `${now.getFullYear()}-${(now.getMonth()+1).toString().padStart(2, '0')}-${now.getDate().toString().padStart(2, '0')} ${now.getHours().toString().padStart(2, '0')}:${now.getMinutes().toString().padStart(2, '0')}:${now.getSeconds().toString().padStart(2, '0')}`;
    }
  }
}
</script>

<style scoped>
.fullscreen-iframe-container {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
  background: linear-gradient(135deg, #1a2a6c, #2c3e50);
  color: white;
  font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
  overflow: hidden;
}

/* 顶部标题栏样式 */
.system-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: 15px 30px;
  background: rgba(0, 0, 0, 0.7);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
  z-index: 100;
}

.logo {
  display: flex;
  align-items: center;
  gap: 15px;
}

.logo-icon {
  width: 40px;
  height: 40px;
  background: linear-gradient(135deg, #3498db, #2c3e50);
  border-radius: 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-weight: bold;
  font-size: 20px;
}

.system-title {
  font-size: 1.5rem;
  font-weight: 600;
  letter-spacing: 0.5px;
}

.user-info {
  display: flex;
  gap: 25px;
  font-size: 0.95rem;
}

.user-item {
  display: flex;
  align-items: center;
}

.label {
  opacity: 0.8;
}

.value {
  font-weight: 500;
}

/* 主内容区样式 */
.iframe-wrapper {
  flex: 1;
  position: relative;
  overflow: hidden;
}

.embedded-frame {
  width: 100%;
  height: 100%;
  border: none;
  background: white;
}

/* 加载动画样式 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(0, 0, 0, 0.85);
  display: flex;
  justify-content: center;
  align-items: center;
  z-index: 10;
  transition: opacity 0.5s ease;
}

.spinner-container {
  text-align: center;
}

.spinner {
  width: 60px;
  height: 60px;
  border: 5px solid rgba(255, 255, 255, 0.1);
  border-top: 5px solid #3498db;
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin: 0 auto 20px;
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 1.4rem;
  margin-bottom: 10px;
}

.loading-subtext {
  font-size: 1rem;
  opacity: 0.7;
}

/* 状态指示器 */
.connection-status {
  position: absolute;
  top: 20px;
  right: 30px;
  background: rgba(0, 0, 0, 0.6);
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 5;
}

/* 代理切换按钮 */
.proxy-toggle {
  position: absolute;
  top: 20px;
  left: 30px;
  z-index: 5;
}

.toggle-btn {
  background: rgba(52, 152, 219, 0.8);
  color: white;
  border: none;
  padding: 8px 15px;
  border-radius: 20px;
  font-size: 0.9rem;
  cursor: pointer;
  transition: background-color 0.3s ease;
}

.toggle-btn:hover {
  background: rgba(52, 152, 219, 1);
}

.status-indicator {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  display: inline-block;
}

.status-connecting .status-indicator {
  background-color: #f39c12;
  animation: pulse 1.5s infinite;
}

.status-connected .status-indicator {
  background-color: #2ecc71;
}

.status-error .status-indicator {
  background-color: #e74c3c;
  animation: pulse 0.8s infinite;
}

@keyframes pulse {
  0% { opacity: 0.5; }
  50% { opacity: 1; }
  100% { opacity: 0.5; }
}

/* 系统水印 */
.system-watermark {
  position: absolute;
  bottom: 20px;
  right: 30px;
  font-size: 0.8rem;
  opacity: 0.3;
  z-index: 5;
}

/* 底部状态栏样式 */
.system-footer {
  display: flex;
  justify-content: space-between;
  padding: 12px 30px;
  background: rgba(0, 0, 0, 0.7);
  border-top: 1px solid rgba(255, 255, 255, 0.1);
  font-size: 0.85rem;
}

.status-item {
  display: flex;
  align-items: center;
}

.status-item .label {
  opacity: 0.7;
}

.status-item .value {
  font-weight: 500;
}
</style>
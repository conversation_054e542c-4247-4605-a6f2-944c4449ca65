package org.thingsboard.server.controller;

import lombok.extern.slf4j.Slf4j;
import org.springframework.http.*;
import org.springframework.web.bind.annotation.*;
import org.springframework.web.client.RestTemplate;
import org.springframework.web.servlet.ModelAndView;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import java.io.IOException;
import java.net.URI;
import java.util.Enumeration;

/**
 * 工作流系统代理控制器
 * 用于解决跨域问题并注入认证请求头
 */
@RestController
@RequestMapping("/api/workflow")
@Slf4j
public class WorkflowProxyController {

    private static final String WORKFLOW_BASE_URL = "http://10.6.5.224:5666";
    private static final String CLIENT_ID = "e5cd7e4891bf95d1d19206ce24a7b32e";
    private static final String AUTHORIZATION_TOKEN = "Bearer eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzI1NiJ9.eyJsb2dpblR5cGUiOiJsb2dpbiIsImxvZ2luSWQiOiJzeXNfdXNlcjoxIiwicm5TdHIiOiIzRjI4SXI4TXUxeWpuUjIwWmNJVGJJaGw5VmVmenpGMSIsImNsaWVudGlkIjoiZTVjZDdlNDg5MWJmOTVkMWQxOTIwNmNlMjRhN2IzMmUiLCJ0ZW5hbnRJZCI6IjAwMDAwMCIsInVzZXJJZCI6MSwidXNlck5hbWUiOiJhZG1pbiIsImRlcHRJZCI6MTAwLCJkZXB0TmFtZSI6IuawtOWKoeWxgCIsImRlcHRDYXRlZ29yeSI6IiJ9.ySyVUcx5O_Eb5XwnZWehFWXpGvAyVk5h53dGd2-kP2I";

    private final RestTemplate restTemplate = new RestTemplate();

    /**
     * 代理工作流页面请求
     */
    @GetMapping("/proxy/**")
    public void proxyWorkflowPage(HttpServletRequest request, HttpServletResponse response) throws IOException {
        String requestPath = request.getRequestURI().replace("/api/workflow/proxy", "");
        if (requestPath.isEmpty()) {
            requestPath = "/workflow/processDefinition";
        }
        
        String targetUrl = WORKFLOW_BASE_URL + requestPath;
        if (request.getQueryString() != null) {
            targetUrl += "?" + request.getQueryString();
        }

        log.info("代理工作流请求: {}", targetUrl);

        try {
            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("clientid", CLIENT_ID);
            headers.set("authorization", AUTHORIZATION_TOKEN);
            headers.set("User-Agent", "Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36");
            
            // 复制原始请求的其他头信息（除了认证相关的）
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                if (!headerName.equalsIgnoreCase("authorization") && 
                    !headerName.equalsIgnoreCase("clientid") &&
                    !headerName.equalsIgnoreCase("host")) {
                    headers.set(headerName, request.getHeader(headerName));
                }
            }

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<String> proxyResponse = restTemplate.exchange(
                targetUrl, 
                HttpMethod.GET, 
                entity, 
                String.class
            );

            // 设置响应头
            response.setStatus(proxyResponse.getStatusCodeValue());
            proxyResponse.getHeaders().forEach((key, values) -> {
                if (!key.equalsIgnoreCase("transfer-encoding") && 
                    !key.equalsIgnoreCase("content-encoding")) {
                    values.forEach(value -> response.addHeader(key, value));
                }
            });

            // 设置内容类型
            if (proxyResponse.getHeaders().getContentType() != null) {
                response.setContentType(proxyResponse.getHeaders().getContentType().toString());
            } else {
                response.setContentType("text/html;charset=UTF-8");
            }

            // 写入响应内容
            String responseBody = proxyResponse.getBody();
            if (responseBody != null) {
                // 替换响应中的链接，使其通过代理访问
                responseBody = responseBody.replaceAll(
                    "(?i)(href|src|action)=\"(/[^\"]*?)\"", 
                    "$1=\"/api/workflow/proxy$2\""
                );
                responseBody = responseBody.replaceAll(
                    "(?i)(href|src|action)='(/[^']*?)'", 
                    "$1='/api/workflow/proxy$2'"
                );
                
                response.getWriter().write(responseBody);
            }

        } catch (Exception e) {
            log.error("代理工作流请求失败: {}", e.getMessage(), e);
            response.setStatus(HttpServletResponse.SC_INTERNAL_SERVER_ERROR);
            response.setContentType("text/html;charset=UTF-8");
            response.getWriter().write(
                "<html><body>" +
                "<h2>工作流系统连接失败</h2>" +
                "<p>无法连接到工作流系统，请检查网络连接或联系管理员。</p>" +
                "<p>错误信息: " + e.getMessage() + "</p>" +
                "</body></html>"
            );
        }
    }

    /**
     * 代理工作流API请求
     */
    @RequestMapping(value = "/api/**", method = {RequestMethod.GET, RequestMethod.POST, RequestMethod.PUT, RequestMethod.DELETE})
    public ResponseEntity<String> proxyWorkflowApi(
            HttpServletRequest request,
            @RequestBody(required = false) String body) {
        
        String requestPath = request.getRequestURI().replace("/api/workflow", "");
        String targetUrl = WORKFLOW_BASE_URL + requestPath;
        if (request.getQueryString() != null) {
            targetUrl += "?" + request.getQueryString();
        }

        log.info("代理工作流API请求: {} {}", request.getMethod(), targetUrl);

        try {
            // 创建请求头
            HttpHeaders headers = new HttpHeaders();
            headers.set("clientid", CLIENT_ID);
            headers.set("authorization", AUTHORIZATION_TOKEN);
            
            // 复制原始请求的头信息
            Enumeration<String> headerNames = request.getHeaderNames();
            while (headerNames.hasMoreElements()) {
                String headerName = headerNames.nextElement();
                if (!headerName.equalsIgnoreCase("authorization") && 
                    !headerName.equalsIgnoreCase("clientid") &&
                    !headerName.equalsIgnoreCase("host") &&
                    !headerName.equalsIgnoreCase("content-length")) {
                    headers.set(headerName, request.getHeader(headerName));
                }
            }

            HttpEntity<String> entity = new HttpEntity<>(body, headers);
            
            HttpMethod method = HttpMethod.valueOf(request.getMethod());
            ResponseEntity<String> proxyResponse = restTemplate.exchange(
                targetUrl, 
                method, 
                entity, 
                String.class
            );

            return ResponseEntity.status(proxyResponse.getStatusCode())
                    .headers(proxyResponse.getHeaders())
                    .body(proxyResponse.getBody());

        } catch (Exception e) {
            log.error("代理工作流API请求失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.INTERNAL_SERVER_ERROR)
                    .body("{\"error\":\"工作流系统连接失败: " + e.getMessage() + "\"}");
        }
    }

    /**
     * 代理静态资源请求
     */
    @GetMapping("/static/**")
    public ResponseEntity<byte[]> proxyStaticResource(HttpServletRequest request) {
        String requestPath = request.getRequestURI().replace("/api/workflow", "");
        String targetUrl = WORKFLOW_BASE_URL + requestPath;
        if (request.getQueryString() != null) {
            targetUrl += "?" + request.getQueryString();
        }

        log.debug("代理静态资源请求: {}", targetUrl);

        try {
            HttpHeaders headers = new HttpHeaders();
            headers.set("clientid", CLIENT_ID);
            headers.set("authorization", AUTHORIZATION_TOKEN);

            HttpEntity<String> entity = new HttpEntity<>(headers);
            ResponseEntity<byte[]> proxyResponse = restTemplate.exchange(
                targetUrl, 
                HttpMethod.GET, 
                entity, 
                byte[].class
            );

            return ResponseEntity.status(proxyResponse.getStatusCode())
                    .headers(proxyResponse.getHeaders())
                    .body(proxyResponse.getBody());

        } catch (Exception e) {
            log.error("代理静态资源请求失败: {}", e.getMessage(), e);
            return ResponseEntity.status(HttpStatus.NOT_FOUND).build();
        }
    }
}
